{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}, "description": "The keccak256 hash function for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/keccak256", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/keccak256", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x37a7d1dd604198317d0d61c892bbdcff7f8c88420f5e6f5dd9c73c2dc0de026a", "types": "./lib/index.d.ts", "version": "5.7.0"}