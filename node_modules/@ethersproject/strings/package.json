{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}, "description": "String utility functions.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers", "strings", "utf8"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/strings", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/strings", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xbde9a4dad59a25a6b1956cf36146ddcceb251b39b2761ac4899b24876656c496", "types": "./lib/index.d.ts", "version": "5.7.0"}