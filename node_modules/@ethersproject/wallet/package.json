{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/json-wallets": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}, "description": "Classes for managing, encrypting and decrypting Ethereum private keys as a Signer for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/wallet", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/wallet", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x9f0366a56a1e73f4f47b37c7167c0491792f347492688f07f964e12fc10b5a1a", "types": "./lib/index.d.ts", "version": "5.7.0"}