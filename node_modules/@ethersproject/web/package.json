{"_ethers.alias": {"geturl.js": "browser-geturl.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/geturl": "./lib/browser-geturl.js"}, "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}, "description": "Utility fucntions for managing web requests for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "44cbc7fa4e199c1d6113ceec3c5162f53def5bb8", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/web", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/web", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x37945bd0bcbf905a641db7e631bb538570042dbe6513e1e4cdd088f25b4ec274", "types": "./lib/index.d.ts", "version": "5.7.1"}